2025-08-01 16:22:01,080 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 48] for text length 47 at line 4210
2025-08-01 16:22:01,086 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 48] for text length 46 at line 4258
2025-08-01 16:22:01,090 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 55] for text length 54 at line 4279
2025-08-01 16:22:01,090 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 54] for text length 53 at line 4306
2025-08-01 16:22:01,092 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 48] for text length 46 at line 4330
2025-08-01 16:22:01,092 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 45] for text length 43 at line 4354
2025-08-01 16:22:01,092 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [29, 45] for text length 42 at line 4359
2025-08-01 16:22:01,092 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 60 at line 4364
2025-08-01 16:22:01,092 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 48 at line 4365
2025-08-01 16:22:01,097 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 37] for text length 36 at line 4371
2025-08-01 16:22:01,098 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 60 at line 4389
2025-08-01 16:22:01,098 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 47] for text length 45 at line 4411
2025-08-01 16:22:01,098 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [56, 60] for text length 59 at line 4413
2025-08-01 16:22:01,098 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4414
2025-08-01 16:22:01,098 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 48 at line 4424
2025-08-01 16:22:01,098 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [46, 66] for text length 64 at line 4425
2025-08-01 16:22:01,102 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4427
2025-08-01 16:22:01,102 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4433
2025-08-01 16:22:01,102 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4437
2025-08-01 16:22:01,103 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [68, 72] for text length 71 at line 4446
2025-08-01 16:22:01,103 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [46, 70] for text length 68 at line 4449
2025-08-01 16:22:01,105 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4461
2025-08-01 16:22:01,105 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [50, 54] for text length 53 at line 4465
2025-08-01 16:22:01,105 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 44] for text length 42 at line 4475
2025-08-01 16:22:01,105 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [56, 60] for text length 59 at line 4480
2025-08-01 16:22:01,109 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [56, 60] for text length 58 at line 4487
2025-08-01 16:22:01,111 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4499
2025-08-01 16:22:01,111 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4500
2025-08-01 16:22:01,111 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4509
2025-08-01 16:22:01,111 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4512
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4517
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4529
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4533
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4535
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4547
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4548
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4557
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4560
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4565
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4577
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4581
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4583
2025-08-01 16:22:01,112 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4595
2025-08-01 16:22:01,119 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4596
2025-08-01 16:22:01,119 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4605
2025-08-01 16:22:01,119 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4608
2025-08-01 16:22:01,119 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4613
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4625
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4629
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4631
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4643
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4644
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4653
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4656
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4661
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4673
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4677
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4679
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4691
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4692
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4701
2025-08-01 16:22:01,121 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4704
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4709
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4721
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4725
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4727
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4739
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4740
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4749
2025-08-01 16:22:01,131 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4752
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4757
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4769
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4773
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4775
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4787
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4788
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4797
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4800
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4805
2025-08-01 16:22:01,134 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4817
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4821
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4823
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4835
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4836
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4845
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4848
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4853
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4865
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4869
2025-08-01 16:22:01,140 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4871
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4883
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4884
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4893
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4896
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4901
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4913
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4917
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4919
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4931
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4932
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4941
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4944
2025-08-01 16:22:01,144 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4949
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 4961
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4965
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 4967
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 4979
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 4980
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 4989
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 4992
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 4997
2025-08-01 16:22:01,150 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 5009
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5013
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 5015
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 5027
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 5028
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 5037
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 5040
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5045
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 5057
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5061
2025-08-01 16:22:01,154 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 5063
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 5075
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 5076
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 5085
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 5088
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5093
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 5105
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5109
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 5111
2025-08-01 16:22:01,162 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 5123
2025-08-01 16:22:01,166 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 5124
2025-08-01 16:22:01,166 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 5133
2025-08-01 16:22:01,166 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 5136
2025-08-01 16:22:01,166 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5141
2025-08-01 16:22:01,169 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [52, 56] for text length 55 at line 5153
2025-08-01 16:22:01,169 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5157
2025-08-01 16:22:01,169 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 5159
2025-08-01 16:22:01,169 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 63 at line 5171
2025-08-01 16:22:01,170 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 53] for text length 52 at line 5172
2025-08-01 16:22:01,170 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 58 at line 5181
2025-08-01 16:22:01,170 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 47 at line 5184
2025-08-01 16:22:01,172 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 57 at line 5189
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 5206
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 5258
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [71, 75] for text length 70 at line 5262
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 56] for text length 55 at line 5265
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [48, 52] for text length 49 at line 5267
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [69, 73] for text length 71 at line 5268
2025-08-01 16:22:01,173 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 5285
2025-08-01 16:22:01,176 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 46 at line 5316
2025-08-01 16:22:01,177 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 62] for text length 60 at line 5330
2025-08-01 16:22:01,178 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 56] for text length 55 at line 5335
2025-08-01 16:22:01,178 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 58] for text length 55 at line 5360
2025-08-01 16:22:01,179 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 59 at line 5365
2025-08-01 16:22:01,179 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 50] for text length 46 at line 5368
2025-08-01 16:22:01,179 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [48, 57] for text length 56 at line 5369
2025-08-01 16:22:01,179 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [53, 57] for text length 56 at line 5425
2025-08-01 16:22:01,179 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 45 at line 5439
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [70, 74] for text length 72 at line 5443
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 59 at line 5451
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [51, 60] for text length 58 at line 5466
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 58 at line 5466
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 5467
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [69, 73] for text length 69 at line 5481
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 47 at line 5484
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 49] for text length 45 at line 5489
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [48, 57] for text length 54 at line 5512
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [66, 70] for text length 66 at line 5530
2025-08-01 16:22:01,182 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 44 at line 5546
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [73, 77] for text length 74 at line 5552
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 50] for text length 46 at line 5594
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 71] for text length 68 at line 5595
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [37, 46] for text length 45 at line 5599
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 47 at line 5604
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 57 at line 5605
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 5611
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [70, 74] for text length 71 at line 5618
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 50] for text length 47 at line 5620
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 49] for text length 46 at line 5647
2025-08-01 16:22:01,187 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [66, 74] for text length 72 at line 5649
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [75, 79] for text length 72 at line 5649
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 5662
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [66, 70] for text length 68 at line 5666
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 47 at line 5669
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 50] for text length 49 at line 5674
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [67, 71] for text length 69 at line 5703
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 5719
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 50] for text length 48 at line 5724
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 66] for text length 64 at line 5738
2025-08-01 16:22:01,197 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 59] for text length 57 at line 5747
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 5750
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 46 at line 5761
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 65] for text length 62 at line 5762
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 46 at line 5773
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [71, 75] for text length 74 at line 5775
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 46] for text length 44 at line 5783
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 47 at line 5797
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [70, 74] for text length 70 at line 5806
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 51] for text length 50 at line 5807
2025-08-01 16:22:01,203 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 48 at line 5808
2025-08-01 16:22:01,207 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 59] for text length 58 at line 5811
2025-08-01 16:22:01,207 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 46] for text length 44 at line 5819
2025-08-01 16:22:01,207 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 65] for text length 62 at line 5822
2025-08-01 16:22:01,207 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 50] for text length 48 at line 5837
2025-08-01 16:22:01,207 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 46 at line 5845
2025-08-01 16:22:01,210 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 65] for text length 62 at line 5846
2025-08-01 16:22:01,210 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 46 at line 5857
2025-08-01 16:22:01,210 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [71, 75] for text length 74 at line 5859
2025-08-01 16:22:01,211 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 46] for text length 44 at line 5867
2025-08-01 16:22:01,211 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 47 at line 5881
2025-08-01 16:22:01,213 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [70, 74] for text length 70 at line 5890
2025-08-01 16:22:01,213 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 51] for text length 50 at line 5891
2025-08-01 16:22:01,213 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 49] for text length 48 at line 5892
2025-08-01 16:22:01,214 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 59] for text length 58 at line 5895
2025-08-01 16:22:01,214 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 54] for text length 53 at line 5907
2025-08-01 16:22:01,215 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 53 at line 5907
2025-08-01 16:22:01,215 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [51, 55] for text length 54 at line 5910
2025-08-01 16:22:01,215 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [59, 63] for text length 58 at line 5912
2025-08-01 16:22:01,217 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [59, 66] for text length 64 at line 5926
2025-08-01 16:22:01,217 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 59 at line 5941
2025-08-01 16:22:01,217 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 71 at line 5949
2025-08-01 16:22:01,217 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 59 at line 5980
2025-08-01 16:22:01,217 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [63, 70] for text length 69 at line 5988
2025-08-01 16:22:01,220 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [56, 60] for text length 57 at line 6019
2025-08-01 16:22:01,220 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [63, 70] for text length 69 at line 6027
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 58 at line 6058
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 68] for text length 67 at line 6066
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 58 at line 6097
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [62, 69] for text length 68 at line 6105
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [59, 63] for text length 60 at line 6136
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [62, 69] for text length 68 at line 6144
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [62, 66] for text length 63 at line 6175
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [64, 71] for text length 70 at line 6183
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [60, 64] for text length 61 at line 6214
2025-08-01 16:22:01,226 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 62 at line 6253
2025-08-01 16:22:01,236 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 62 at line 6292
2025-08-01 16:22:01,236 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [60, 64] for text length 61 at line 6331
2025-08-01 16:22:01,236 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 65] for text length 62 at line 6370
2025-08-01 16:22:01,236 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 59 at line 6409
2025-08-01 16:22:01,236 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 59 at line 6448
2025-08-01 16:22:01,240 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [56, 60] for text length 57 at line 6487
2025-08-01 16:22:01,241 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 58 at line 6526
2025-08-01 16:22:01,241 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 59 at line 6565
2025-08-01 16:22:01,241 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [58, 62] for text length 61 at line 6594
2025-08-01 16:22:01,243 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 62] for text length 60 at line 6598
2025-08-01 16:22:01,243 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [57, 61] for text length 59 at line 6600
2025-08-01 16:22:01,243 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [56, 73] for text length 68 at line 6614
2025-08-01 16:22:01,243 | __main__:73 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Skipping invalid entity format: [33, 37, 41, 'MONTH'] at line 6734
2025-08-01 16:22:01,261 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [55, 59] for text length 56 at line 7492
2025-08-01 16:22:01,262 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 42] for text length 40 at line 7493
2025-08-01 16:22:01,262 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [60, 64] for text length 60 at line 7494
2025-08-01 16:22:01,269 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 55 at line 8181
2025-08-01 16:22:01,269 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 53 at line 8252
2025-08-01 16:22:01,273 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 53 at line 8323
2025-08-01 16:22:01,273 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 57] for text length 56 at line 8385
2025-08-01 16:22:01,273 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 52 at line 8394
2025-08-01 16:22:01,277 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 57] for text length 54 at line 8456
2025-08-01 16:22:01,277 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 54 at line 8465
2025-08-01 16:22:01,279 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 57] for text length 54 at line 8527
2025-08-01 16:22:01,280 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 54 at line 8536
2025-08-01 16:22:01,281 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 55 at line 8613
2025-08-01 16:22:01,281 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 53 at line 8684
2025-08-01 16:22:01,284 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 53 at line 8755
2025-08-01 16:22:01,287 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 57] for text length 56 at line 8817
2025-08-01 16:22:01,290 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 56] for text length 52 at line 8826
2025-08-01 16:22:01,297 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 68] for text length 65 at line 8939
2025-08-01 16:22:01,297 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 8955
2025-08-01 16:22:01,300 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 8995
2025-08-01 16:22:01,300 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9025
2025-08-01 16:22:01,300 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9055
2025-08-01 16:22:01,300 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9085
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9115
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9145
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9175
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9205
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9235
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9265
2025-08-01 16:22:01,304 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9295
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9325
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9355
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9385
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9415
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9445
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9475
2025-08-01 16:22:01,310 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9505
2025-08-01 16:22:01,316 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9535
2025-08-01 16:22:01,317 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9565
2025-08-01 16:22:01,318 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9595
2025-08-01 16:22:01,318 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9625
2025-08-01 16:22:01,318 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9655
2025-08-01 16:22:01,318 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9685
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [65, 72] for text length 68 at line 9715
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [67, 84] for text length 75 at line 9742
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [76, 83] for text length 77 at line 9744
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [64, 81] for text length 76 at line 9746
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [54, 65] for text length 61 at line 9747
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [64, 71] for text length 70 at line 9758
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [62, 68] for text length 67 at line 9762
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [66, 75] for text length 74 at line 9775
2025-08-01 16:22:01,321 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [50, 66] for text length 65 at line 9776
2025-08-01 16:22:01,331 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 60] for text length 59 at line 9804
2025-08-01 16:22:01,331 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 52] for text length 51 at line 9811
2025-08-01 16:22:01,331 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 59] for text length 55 at line 9815
2025-08-01 16:22:01,331 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 53] for text length 52 at line 9893
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 38] for text length 36 at line 9940
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 9944
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 9966
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 9990
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10036
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 10052
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 10074
2025-08-01 16:22:01,334 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 10098
2025-08-01 16:22:01,340 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10144
2025-08-01 16:22:01,340 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 10160
2025-08-01 16:22:01,340 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 10182
2025-08-01 16:22:01,340 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 10206
2025-08-01 16:22:01,340 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10252
2025-08-01 16:22:01,340 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 10268
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 10290
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 10314
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10360
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 10376
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 10398
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 10422
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10468
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 10484
2025-08-01 16:22:01,345 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 10506
2025-08-01 16:22:01,351 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 10530
2025-08-01 16:22:01,352 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10576
2025-08-01 16:22:01,352 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 58] for text length 57 at line 10592
2025-08-01 16:22:01,354 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 39] for text length 36 at line 10614
2025-08-01 16:22:01,354 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 50] for text length 49 at line 10638
2025-08-01 16:22:01,362 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [61, 67] for text length 65 at line 10723
2025-08-01 16:22:01,363 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 47] for text length 45 at line 10737
2025-08-01 16:22:01,363 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 10739
2025-08-01 16:22:01,364 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 47] for text length 45 at line 10753
2025-08-01 16:22:01,364 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [32, 39] for text length 37 at line 10766
2025-08-01 16:22:01,364 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 55] for text length 52 at line 10775
2025-08-01 16:22:01,364 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 48] for text length 47 at line 10776
2025-08-01 16:22:01,364 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 54] for text length 53 at line 10784
2025-08-01 16:22:01,364 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 44] for text length 43 at line 10785
2025-08-01 16:22:01,367 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 10796
2025-08-01 16:22:01,367 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10801
2025-08-01 16:22:01,367 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 10803
2025-08-01 16:22:01,370 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 10813
2025-08-01 16:22:01,371 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 10815
2025-08-01 16:22:01,371 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 10819
2025-08-01 16:22:01,371 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 10820
2025-08-01 16:22:01,371 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 10827
2025-08-01 16:22:01,372 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 10835
2025-08-01 16:22:01,372 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 10837
2025-08-01 16:22:01,374 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 10843
2025-08-01 16:22:01,374 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 10844
2025-08-01 16:22:01,375 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 10847
2025-08-01 16:22:01,375 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 10857
2025-08-01 16:22:01,375 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 10861
2025-08-01 16:22:01,375 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 10863
2025-08-01 16:22:01,375 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 10871
2025-08-01 16:22:01,377 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 10880
2025-08-01 16:22:01,377 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10885
2025-08-01 16:22:01,377 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 10887
2025-08-01 16:22:01,377 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 10897
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 10899
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 10903
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 10904
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 10911
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 10919
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 10921
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 10927
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 10928
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 10931
2025-08-01 16:22:01,380 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 10941
2025-08-01 16:22:01,385 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 10945
2025-08-01 16:22:01,385 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 10947
2025-08-01 16:22:01,386 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 10955
2025-08-01 16:22:01,386 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 10964
2025-08-01 16:22:01,387 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 10969
2025-08-01 16:22:01,387 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 10971
2025-08-01 16:22:01,389 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 10981
2025-08-01 16:22:01,389 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 10983
2025-08-01 16:22:01,389 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 10987
2025-08-01 16:22:01,394 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 10988
2025-08-01 16:22:01,395 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 10995
2025-08-01 16:22:01,396 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 11003
2025-08-01 16:22:01,397 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 11005
2025-08-01 16:22:01,397 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 11011
2025-08-01 16:22:01,397 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 11012
2025-08-01 16:22:01,397 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 11015
2025-08-01 16:22:01,397 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 11025
2025-08-01 16:22:01,397 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 11029
2025-08-01 16:22:01,400 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 11031
2025-08-01 16:22:01,400 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 11039
2025-08-01 16:22:01,400 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 11048
2025-08-01 16:22:01,400 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 11053
2025-08-01 16:22:01,400 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 11055
2025-08-01 16:22:01,402 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 11065
2025-08-01 16:22:01,402 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 11067
2025-08-01 16:22:01,403 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 11071
2025-08-01 16:22:01,404 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 11072
2025-08-01 16:22:01,404 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 11079
2025-08-01 16:22:01,406 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 11087
2025-08-01 16:22:01,406 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 11089
2025-08-01 16:22:01,408 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 11095
2025-08-01 16:22:01,409 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 11096
2025-08-01 16:22:01,410 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 11099
2025-08-01 16:22:01,410 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 11109
2025-08-01 16:22:01,411 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 11113
2025-08-01 16:22:01,411 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 11115
2025-08-01 16:22:01,412 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 11123
2025-08-01 16:22:01,413 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 11132
2025-08-01 16:22:01,413 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 11137
2025-08-01 16:22:01,413 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 11139
2025-08-01 16:22:01,414 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 11149
2025-08-01 16:22:01,414 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 11151
2025-08-01 16:22:01,414 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 11155
2025-08-01 16:22:01,415 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 11156
2025-08-01 16:22:01,415 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 11163
2025-08-01 16:22:01,416 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 11171
2025-08-01 16:22:01,417 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 11173
2025-08-01 16:22:01,417 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 11179
2025-08-01 16:22:01,417 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 11180
2025-08-01 16:22:01,418 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 11183
2025-08-01 16:22:01,418 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 11193
2025-08-01 16:22:01,418 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 11197
2025-08-01 16:22:01,419 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 11199
2025-08-01 16:22:01,420 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 11207
2025-08-01 16:22:01,422 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 11216
2025-08-01 16:22:01,423 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 11221
2025-08-01 16:22:01,423 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 11223
2025-08-01 16:22:01,425 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 11233
2025-08-01 16:22:01,426 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 11235
2025-08-01 16:22:01,427 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 11239
2025-08-01 16:22:01,427 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 11240
2025-08-01 16:22:01,428 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 11247
2025-08-01 16:22:01,429 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 11255
2025-08-01 16:22:01,429 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 11257
2025-08-01 16:22:01,430 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 11263
2025-08-01 16:22:01,431 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 11264
2025-08-01 16:22:01,431 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 11267
2025-08-01 16:22:01,432 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 11277
2025-08-01 16:22:01,432 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 11281
2025-08-01 16:22:01,433 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 11283
2025-08-01 16:22:01,434 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 11291
2025-08-01 16:22:01,434 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 11300
2025-08-01 16:22:01,435 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 42] for text length 41 at line 11305
2025-08-01 16:22:01,435 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [43, 50] for text length 49 at line 11307
2025-08-01 16:22:01,437 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 46 at line 11317
2025-08-01 16:22:01,437 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 42] for text length 41 at line 11319
2025-08-01 16:22:01,439 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 43] for text length 42 at line 11323
2025-08-01 16:22:01,440 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [42, 48] for text length 45 at line 11324
2025-08-01 16:22:01,440 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 48] for text length 47 at line 11331
2025-08-01 16:22:01,440 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 54] for text length 51 at line 11339
2025-08-01 16:22:01,442 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 46] for text length 45 at line 11341
2025-08-01 16:22:01,445 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [34, 40] for text length 39 at line 11347
2025-08-01 16:22:01,445 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 57] for text length 56 at line 11348
2025-08-01 16:22:01,446 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 53] for text length 48 at line 11351
2025-08-01 16:22:01,446 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [35, 44] for text length 42 at line 11361
2025-08-01 16:22:01,446 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 51] for text length 49 at line 11365
2025-08-01 16:22:01,446 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [33, 45] for text length 44 at line 11367
2025-08-01 16:22:01,448 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 51] for text length 50 at line 11375
2025-08-01 16:22:01,448 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 50] for text length 45 at line 11384
2025-08-01 16:22:01,448 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [36, 44] for text length 41 at line 11392
2025-08-01 16:22:01,450 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [59, 62] for text length 59 at line 11394
2025-08-01 16:22:01,450 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [41, 49] for text length 48 at line 11395
2025-08-01 16:22:01,451 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [44, 60] for text length 58 at line 11396
2025-08-01 16:22:01,451 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [67, 70] for text length 58 at line 11396
2025-08-01 16:22:01,452 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [67, 70] for text length 60 at line 11412
2025-08-01 16:22:01,452 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 48] for text length 47 at line 11444
2025-08-01 16:22:01,453 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [49, 55] for text length 53 at line 11463
2025-08-01 16:22:01,455 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11499
2025-08-01 16:22:01,457 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11535
2025-08-01 16:22:01,460 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11571
2025-08-01 16:22:01,462 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11607
2025-08-01 16:22:01,464 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11643
2025-08-01 16:22:01,465 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11679
2025-08-01 16:22:01,465 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11715
2025-08-01 16:22:01,465 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 53] for text length 51 at line 11751
2025-08-01 16:22:01,465 | __main__:87 | INFO     | load_training_data | PID:11940 | TID:MainThread | Loaded 11769 training samples
2025-08-01 16:22:01,465 | __main__:129 | INFO     | train | PID:11940 | TID:MainThread | Loaded 11769 training samples
2025-08-01 16:22:01,465 | __main__:49 | INFO     | load_training_data | PID:11940 | TID:MainThread | Loading training data from: data/training/entity_validation_data.jsonl
2025-08-01 16:22:01,471 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 45 at line 19
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [38, 46] for text length 43 at line 30
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [47, 51] for text length 43 at line 30
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [45, 49] for text length 45 at line 37
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [46, 50] for text length 46 at line 42
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [39, 43] for text length 40 at line 61
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [30, 39] for text length 36 at line 62
2025-08-01 16:22:01,473 | __main__:71 | WARNING  | load_training_data | PID:11940 | TID:MainThread | Invalid span [40, 44] for text length 36 at line 62
2025-08-01 16:22:01,479 | __main__:87 | INFO     | load_training_data | PID:11940 | TID:MainThread | Loaded 100 training samples
2025-08-01 16:22:01,479 | __main__:135 | INFO     | train | PID:11940 | TID:MainThread | Loaded 100 validation samples
2025-08-01 16:22:01,487 | __main__:102 | INFO     | get_unique_labels | PID:11940 | TID:MainThread | Found 24 unique entity labels: ['BENEFIT_TYPE', 'DATE', 'DEPARTMENT', 'DOCUMENT_TYPE', 'EMPLOYEE_NAME', 'HR_EVENT', 'HR_PROCESS', 'LEAVE_BALANCE', 'LEAVE_TYPE', 'MONTH', 'O', 'POLICY_NAME', 'YEAR', 'amount', 'download_payslip', 'expense_type', 'intent_routing', 'issue_type', 'ner', 'payslip_context', 'salary_component', 'salary_type', 'summarization', 'text_extraction']  
2025-08-01 16:22:01,688 | __main__:150 | INFO     | train | PID:11940 | TID:MainThread | Initializing SpanMarker model from microsoft/deberta-v3-base
C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv\lib\site-packages\transformers\convert_slow_tokenizer.py:561: UserWarning: The sentencepiece tokenizer that you are converting to a fast tokenizer uses the byte fallback option which is not implemented in the fast tokenizers. In practice this means that the fast version of the tokenizer can produce unknown tokens whereas the sentencepiece version would have converted these unknown tokens into a sequence of byte tokens matching the original piece of text.
  warnings.warn(
C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv\lib\site-packages\transformers\training_args.py:1594: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
2025-08-01 16:22:07,488 | span_marker.label_normalizer:99 | INFO     | __init__ | PID:11940 | TID:MainThread | No labeling scheme detected: all label IDs belong to individual entity classes.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
C:\Generative AI Projects\Multi-Model RAG Chatbot-react\venv\lib\site-packages\span_marker\trainer.py:140: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  super().__init__(
2025-08-01 16:22:12,854 | __main__:198 | INFO     | train | PID:11940 | TID:MainThread | Starting training...
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
2025-08-01 16:22:13,088 | __main__:280 | ERROR    | main | PID:11940 | TID:MainThread | Training failed: The train dataset must contain a 'tokens' column.

❌ Training failed: The train dataset must contain a 'tokens' column.